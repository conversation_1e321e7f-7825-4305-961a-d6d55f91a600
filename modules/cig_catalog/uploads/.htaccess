# Deny direct access to uploaded files
# Allow only specific file types to be served

# Deny execution of PHP files
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Allow only specific file types
<FilesMatch "\.(jpg|jpeg|png|gif|webp|pdf|zip|doc|docx|xls|xlsx)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Deny access to all other files
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|webp|pdf|zip|doc|docx|xls|xlsx)$).*$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options DENY
    Header set X-XSS-Protection "1; mode=block"
</IfModule>

# Disable directory browsing
Options -Indexes

# Prevent access to hidden files
<FilesMatch "^\.">
    Order Deny,Allow
    Deny from all
</FilesMatch>
