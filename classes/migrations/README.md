# CIG Katalogy Module - Migration System

Kompletní systém pro správu databázových migrací a verzí modulu.

## Přehled

Migrační systém umožňuje:
- ✅ Automatické aktualizace databázové struktury
- ✅ Postupné migrace mezi verzemi
- ✅ Rollback mechanismus při chybách
- ✅ Backup a restore funkcionalita
- ✅ Data integrity kontroly
- ✅ Logging všech operací

## Struktura

### Hlavní třídy

#### MigrationManager.php
Hlavní třída pro orchestraci migrací:
- Detekce aktuální verze modulu
- Automatické spouštění potřebných migrací
- Backup před každou migrací
- Rollback při neúspěchu
- Logging všech operací

#### AbstractMigration.php
Abstraktní základní třída pro všechny migrace:
- Helper metody pro databázové operace
- Validace migrace
- <PERSON>z<PERSON><PERSON><PERSON><PERSON>/odeb<PERSON><PERSON><PERSON><PERSON> sloupců a indexů
- Správa konfiguračních hodnot

### Konkrétní migrace

#### Migration_1_0_0_to_1_1_0.php
**Verze:** 1.0.0 → 1.1.0  
**Popis:** Přidává cache podporu a SEO optimalizace

**Změny:**
- ✅ Cache konfigurace (enabled, lifetime, key prefix)
- ✅ SEO konfigurace (friendly URLs, meta templates)
- ✅ Nové sloupce: meta_title, meta_description, slug
- ✅ Performance indexy (title+active, date_add, position+active)
- ✅ Automatické generování slugů z existujících dat
- ✅ Unique index pro slugy

#### Migration_1_1_0_to_1_2_0.php
**Verze:** 1.1.0 → 1.2.0  
**Popis:** Přidává podporu kategorií pro lepší organizaci

**Změny:**
- ✅ Nová tabulka `cig_catalog_category`
- ✅ Nová tabulka `cig_catalog_category_lang`
- ✅ Sloupec `id_category` v hlavní tabulce katalogů
- ✅ Foreign key constraints
- ✅ 5 výchozích kategorií (Elektronika, Nábytek, Móda, Automotive, Ostatní)
- ✅ Multijazyčná podpora kategorií
- ✅ Konfigurace pro kategorie

#### Migration_1_2_0_to_1_3_0.php
**Verze:** 1.2.0 → 1.3.0  
**Popis:** Přidává pokročilé SEO funkce a analytics

**Změny:**
- ✅ Nová tabulka `cig_catalog_analytics`
- ✅ Open Graph meta tagy (og_title, og_description, og_image)
- ✅ Twitter Cards (twitter_title, twitter_description, twitter_image)
- ✅ Canonical URLs a robots meta
- ✅ Schema.org structured data
- ✅ Analytics konfigurace (Google Analytics, GTM, Facebook Pixel)
- ✅ Tracking views, downloads, orders

## Použití

### Základní použití

```php
// Vytvoření migration manageru
$manager = new MigrationManager('1.3.0', true); // target version, debug mode

// Kontrola, zda je migrace potřebná
if ($manager->isMigrationNeeded()) {
    echo "Migrace je potřebná z verze " . $manager->getCurrentVersion();
    
    // Spuštění migrací
    try {
        $manager->runMigrations();
        echo "Migrace dokončena úspěšně!";
    } catch (Exception $e) {
        echo "Chyba při migraci: " . $e->getMessage();
        // Rollback se spustí automaticky
    }
}
```

### Pokročilé použití

```php
// Migrace na konkrétní verzi
$manager = new MigrationManager('1.2.0');

// Získání logu migrací
$log = $manager->getLog();
foreach ($log as $entry) {
    echo $entry . "\n";
}

// Vyčištění starých logů
$manager->clearOldLogs();
```

### Rollback

```php
// Manuální rollback
$manager = new MigrationManager();
$manager->rollback();
```

## Vytvoření nové migrace

### 1. Vytvoření souboru

Název souboru: `Migration_X_Y_Z_to_A_B_C.php`

```php
<?php
require_once dirname(__FILE__) . '/AbstractMigration.php';

class Migration_1_3_0_to_1_4_0 extends AbstractMigration
{
    protected $fromVersion = '1.3.0';
    protected $toVersion = '1.4.0';

    public function up()
    {
        // Implementace upgrade
        $this->addColumnIfNotExists('cig_catalog', 'new_column', 'VARCHAR(255) DEFAULT NULL');
        $this->addConfigIfNotExists('NEW_CONFIG', 'default_value');
        return true;
    }

    public function down()
    {
        // Implementace rollback
        $this->dropColumnIfExists('cig_catalog', 'new_column');
        $this->removeConfig('NEW_CONFIG');
        return true;
    }

    public function getDescription()
    {
        return 'Popis změn v této migraci';
    }
}
```

### 2. Helper metody

AbstractMigration poskytuje užitečné helper metody:

#### Databázové operace
- `tableExists($tableName)` - kontrola existence tabulky
- `columnExists($tableName, $columnName)` - kontrola existence sloupce
- `indexExists($tableName, $indexName)` - kontrola existence indexu

#### Správa sloupců
- `addColumnIfNotExists($table, $column, $definition, $after)` - přidání sloupce
- `dropColumnIfExists($table, $column)` - odstranění sloupce

#### Správa indexů
- `addIndexIfNotExists($table, $index, $columns, $type)` - přidání indexu
- `dropIndexIfExists($table, $index)` - odstranění indexu

#### Konfigurace
- `addConfigIfNotExists($name, $value)` - přidání konfigurace
- `removeConfig($name)` - odstranění konfigurace
- `updateConfig($name, $value)` - aktualizace konfigurace
- `getConfig($name)` - získání konfigurace

#### SQL operace
- `executeQuery($sql, $usePrefix)` - spuštění SQL dotazu
- `executeQueries($queries, $usePrefix)` - spuštění více dotazů

## Bezpečnost

### Automatické zálohy
- Před každou migrací se vytvoří backup všech tabulek
- Backup tabulky mají suffix `_backup_YYYY_MM_DD_HH_MM_SS`
- Při rollbacku se data obnoví z nejnovějšího backupu

### Error handling
- Všechny chyby jsou zachyceny a zalogované
- Při chybě se automaticky spustí rollback
- Detailní error reporting pro debugging

### Data integrity
- Kontrola orphaned záznamů
- Validace foreign key vztahů
- Kontrola duplicitních hodnot
- Ověření kompletnosti dat

## Monitoring

### Logging
- Všechny operace jsou logované do databáze
- Log obsahuje timestamp, level (info/error/warning) a zprávu
- Automatické čištění starých logů (zachování posledních 50)

### Debug mode
```php
$manager = new MigrationManager('1.3.0', true); // debug enabled
```

V debug módu:
- Všechny SQL dotazy jsou logované
- Detailní výstup do error_log
- Verbose reporting

## Testování

### Spuštění testů
```bash
php test_migrations.php
```

### Test coverage
- ✅ Instantiation všech tříd
- ✅ Validace verzí
- ✅ Existence povinných metod
- ✅ Detekce migračních souborů
- ✅ Výpočet migračních cest
- ✅ Error handling

## Troubleshooting

### Časté problémy

#### "Migration file not found"
- Zkontrolujte název souboru (formát: Migration_X_Y_Z_to_A_B_C.php)
- Ověřte, že soubor je v adresáři classes/migrations/

#### "Migration class not found"
- Název třídy musí odpovídat názvu souboru
- Zkontrolujte require_once pro AbstractMigration

#### "Foreign key constraint fails"
- Zkontrolujte pořadí operací (nejprve data, pak constraints)
- Ověřte, že referenční tabulky existují

#### "Backup table already exists"
- Starý backup nebyl vyčištěn
- Manuálně odstraňte backup tabulky nebo použijte jiný timestamp

### Debug postupy

1. **Zapněte debug mode**
   ```php
   $manager = new MigrationManager('target_version', true);
   ```

2. **Zkontrolujte logy**
   ```php
   $log = $manager->getLog();
   print_r($log);
   ```

3. **Ověřte databázovou strukturu**
   ```sql
   SHOW TABLES LIKE 'ps_cig_catalog%';
   DESCRIBE ps_cig_catalog;
   ```

4. **Kontrola integrity**
   ```bash
   mysql -u user -p database < sql/integrity_check.sql
   ```

## Výkon

### Optimalizace
- Migrace používají transakce pro atomicitu
- Indexy se přidávají až po vložení dat
- Batch operace pro velké datové sady

### Monitoring výkonu
- Měření času jednotlivých kroků
- Memory usage tracking
- SQL query profiling v debug módu

---

**Poznámka:** Vždy testujte migrace na vývojovém prostředí před nasazením do produkce!
