<?php
/**
 * CIG Katalogy Module - Abstract Migration
 * 
 * Base class for all database migrations
 * 
 * <AUTHOR> Team
 * @copyright 2025 CIG
 * @license   Commercial
 * @version   1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

abstract class AbstractMigration
{
    /** @var string From version */
    protected $fromVersion;
    
    /** @var string To version */
    protected $toVersion;
    
    /** @var array SQL queries to execute */
    protected $queries = [];
    
    /** @var array Rollback queries */
    protected $rollbackQueries = [];
    
    /** @var bool Debug mode */
    protected $debug = false;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->debug = defined('_PS_MODE_DEV_') && _PS_MODE_DEV_;
    }

    /**
     * Execute migration (upgrade)
     * 
     * @return bool True on success
     * @throws Exception On migration failure
     */
    abstract public function up();

    /**
     * Rollback migration (downgrade)
     * 
     * @return bool True on success
     * @throws Exception On rollback failure
     */
    abstract public function down();

    /**
     * Get migration description
     * 
     * @return string Migration description
     */
    abstract public function getDescription();

    /**
     * Execute SQL query with error handling
     * 
     * @param string $sql SQL query
     * @param bool $usePrefix Whether to replace PREFIX_ with actual prefix
     * @return bool True on success
     * @throws Exception On SQL error
     */
    protected function executeQuery($sql, $usePrefix = true)
    {
        if ($usePrefix) {
            $sql = str_replace('PREFIX_', _DB_PREFIX_, $sql);
        }
        
        if ($this->debug) {
            error_log('Migration SQL: ' . $sql);
        }
        
        $result = Db::getInstance()->execute($sql);
        
        if (!$result) {
            $error = Db::getInstance()->getMsgError();
            throw new Exception('SQL Error: ' . $error . ' | Query: ' . $sql);
        }
        
        return true;
    }

    /**
     * Execute multiple SQL queries
     * 
     * @param array $queries Array of SQL queries
     * @param bool $usePrefix Whether to replace PREFIX_ with actual prefix
     * @return bool True on success
     * @throws Exception On SQL error
     */
    protected function executeQueries($queries, $usePrefix = true)
    {
        foreach ($queries as $sql) {
            $this->executeQuery($sql, $usePrefix);
        }
        
        return true;
    }

    /**
     * Check if table exists
     * 
     * @param string $tableName Table name (without prefix)
     * @return bool True if table exists
     */
    protected function tableExists($tableName)
    {
        $sql = 'SHOW TABLES LIKE "' . _DB_PREFIX_ . pSQL($tableName) . '"';
        return (bool) Db::getInstance()->getValue($sql);
    }

    /**
     * Check if column exists in table
     * 
     * @param string $tableName Table name (without prefix)
     * @param string $columnName Column name
     * @return bool True if column exists
     */
    protected function columnExists($tableName, $columnName)
    {
        $sql = 'SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE table_schema = DATABASE() 
                AND table_name = "' . _DB_PREFIX_ . pSQL($tableName) . '" 
                AND column_name = "' . pSQL($columnName) . '"';
        
        return (bool) Db::getInstance()->getValue($sql);
    }

    /**
     * Check if index exists on table
     * 
     * @param string $tableName Table name (without prefix)
     * @param string $indexName Index name
     * @return bool True if index exists
     */
    protected function indexExists($tableName, $indexName)
    {
        $sql = 'SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
                WHERE table_schema = DATABASE() 
                AND table_name = "' . _DB_PREFIX_ . pSQL($tableName) . '" 
                AND index_name = "' . pSQL($indexName) . '"';
        
        return (bool) Db::getInstance()->getValue($sql);
    }

    /**
     * Add column to table if it doesn't exist
     * 
     * @param string $tableName Table name (without prefix)
     * @param string $columnName Column name
     * @param string $columnDefinition Column definition (e.g., "VARCHAR(255) DEFAULT NULL")
     * @param string $afterColumn Column after which to add (optional)
     * @return bool True on success
     */
    protected function addColumnIfNotExists($tableName, $columnName, $columnDefinition, $afterColumn = null)
    {
        if ($this->columnExists($tableName, $columnName)) {
            if ($this->debug) {
                error_log('Column ' . $columnName . ' already exists in table ' . $tableName);
            }
            return true;
        }
        
        $sql = 'ALTER TABLE `' . _DB_PREFIX_ . pSQL($tableName) . '` 
                ADD COLUMN `' . pSQL($columnName) . '` ' . $columnDefinition;
        
        if ($afterColumn) {
            $sql .= ' AFTER `' . pSQL($afterColumn) . '`';
        }
        
        return $this->executeQuery($sql, false);
    }

    /**
     * Drop column from table if it exists
     * 
     * @param string $tableName Table name (without prefix)
     * @param string $columnName Column name
     * @return bool True on success
     */
    protected function dropColumnIfExists($tableName, $columnName)
    {
        if (!$this->columnExists($tableName, $columnName)) {
            if ($this->debug) {
                error_log('Column ' . $columnName . ' does not exist in table ' . $tableName);
            }
            return true;
        }
        
        $sql = 'ALTER TABLE `' . _DB_PREFIX_ . pSQL($tableName) . '` 
                DROP COLUMN `' . pSQL($columnName) . '`';
        
        return $this->executeQuery($sql, false);
    }

    /**
     * Add index to table if it doesn't exist
     * 
     * @param string $tableName Table name (without prefix)
     * @param string $indexName Index name
     * @param array $columns Array of column names
     * @param string $indexType Index type (INDEX, UNIQUE, FULLTEXT)
     * @return bool True on success
     */
    protected function addIndexIfNotExists($tableName, $indexName, $columns, $indexType = 'INDEX')
    {
        if ($this->indexExists($tableName, $indexName)) {
            if ($this->debug) {
                error_log('Index ' . $indexName . ' already exists on table ' . $tableName);
            }
            return true;
        }
        
        $columnList = '`' . implode('`, `', array_map('pSQL', $columns)) . '`';
        
        $sql = 'ALTER TABLE `' . _DB_PREFIX_ . pSQL($tableName) . '` 
                ADD ' . $indexType . ' `' . pSQL($indexName) . '` (' . $columnList . ')';
        
        return $this->executeQuery($sql, false);
    }

    /**
     * Drop index from table if it exists
     * 
     * @param string $tableName Table name (without prefix)
     * @param string $indexName Index name
     * @return bool True on success
     */
    protected function dropIndexIfExists($tableName, $indexName)
    {
        if (!$this->indexExists($tableName, $indexName)) {
            if ($this->debug) {
                error_log('Index ' . $indexName . ' does not exist on table ' . $tableName);
            }
            return true;
        }
        
        $sql = 'ALTER TABLE `' . _DB_PREFIX_ . pSQL($tableName) . '` 
                DROP INDEX `' . pSQL($indexName) . '`';
        
        return $this->executeQuery($sql, false);
    }

    /**
     * Insert configuration value if it doesn't exist
     * 
     * @param string $name Configuration name
     * @param string $value Configuration value
     * @return bool True on success
     */
    protected function addConfigIfNotExists($name, $value)
    {
        $sql = 'INSERT IGNORE INTO `' . _DB_PREFIX_ . 'cig_catalog_config` 
                (`name`, `value`, `date_add`, `date_upd`) VALUES
                ("' . pSQL($name) . '", "' . pSQL($value) . '", NOW(), NOW())';
        
        return $this->executeQuery($sql, false);
    }

    /**
     * Remove configuration value
     * 
     * @param string $name Configuration name
     * @return bool True on success
     */
    protected function removeConfig($name)
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'cig_catalog_config` 
                WHERE `name` = "' . pSQL($name) . '"';
        
        return $this->executeQuery($sql, false);
    }

    /**
     * Update configuration value
     * 
     * @param string $name Configuration name
     * @param string $value New configuration value
     * @return bool True on success
     */
    protected function updateConfig($name, $value)
    {
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog_config` 
                SET `value` = "' . pSQL($value) . '", `date_upd` = NOW() 
                WHERE `name` = "' . pSQL($name) . '"';
        
        return $this->executeQuery($sql, false);
    }

    /**
     * Get configuration value
     * 
     * @param string $name Configuration name
     * @return string|null Configuration value or null if not found
     */
    protected function getConfig($name)
    {
        $sql = 'SELECT `value` FROM `' . _DB_PREFIX_ . 'cig_catalog_config` 
                WHERE `name` = "' . pSQL($name) . '" LIMIT 1';
        
        return Db::getInstance()->getValue($sql);
    }

    /**
     * Check if configuration exists
     * 
     * @param string $name Configuration name
     * @return bool True if configuration exists
     */
    protected function configExists($name)
    {
        return $this->getConfig($name) !== false;
    }

    /**
     * Get from version
     * 
     * @return string From version
     */
    public function getFromVersion()
    {
        return $this->fromVersion;
    }

    /**
     * Get to version
     * 
     * @return string To version
     */
    public function getToVersion()
    {
        return $this->toVersion;
    }

    /**
     * Validate migration before execution
     * 
     * @return bool True if migration is valid
     * @throws Exception If migration is invalid
     */
    public function validate()
    {
        if (empty($this->fromVersion) || empty($this->toVersion)) {
            throw new Exception('Migration must define fromVersion and toVersion');
        }
        
        if (version_compare($this->fromVersion, $this->toVersion, '>=')) {
            throw new Exception('To version must be greater than from version');
        }
        
        return true;
    }

    /**
     * Log migration message
     * 
     * @param string $message Log message
     * @param string $level Log level
     */
    protected function log($message, $level = 'info')
    {
        if ($this->debug) {
            error_log('[MIGRATION] [' . strtoupper($level) . '] ' . $message);
        }
    }
}
