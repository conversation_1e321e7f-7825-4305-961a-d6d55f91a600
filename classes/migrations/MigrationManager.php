<?php
/**
 * CIG Katalogy Module - Migration Manager
 * 
 * Manages database migrations and version upgrades
 * 
 * <AUTHOR> Team
 * @copyright 2025 CIG
 * @license   Commercial
 * @version   1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class MigrationManager
{
    /** @var string Current module version */
    private $currentVersion;
    
    /** @var string Target version */
    private $targetVersion;
    
    /** @var array Available migrations */
    private $migrations = [];
    
    /** @var array Migration log */
    private $log = [];
    
    /** @var bool Debug mode */
    private $debug = false;

    /**
     * Constructor
     * 
     * @param string $targetVersion Target version to migrate to
     * @param bool $debug Enable debug mode
     */
    public function __construct($targetVersion = null, $debug = false)
    {
        $this->currentVersion = $this->getCurrentVersion();
        $this->targetVersion = $targetVersion ?: $this->getLatestVersion();
        $this->debug = $debug;
        $this->loadMigrations();
    }

    /**
     * Get current module version from database
     * 
     * @return string Current version
     */
    public function getCurrentVersion()
    {
        if ($this->currentVersion) {
            return $this->currentVersion;
        }

        $sql = 'SELECT `value` FROM `' . _DB_PREFIX_ . 'cig_catalog_config` 
                WHERE `name` = "CIG_CATALOG_VERSION" LIMIT 1';
        
        $version = Db::getInstance()->getValue($sql);
        
        // Default version if not found
        if (!$version) {
            $version = '1.0.0';
            $this->setVersion($version);
        }
        
        $this->currentVersion = $version;
        return $version;
    }

    /**
     * Get latest available version
     * 
     * @return string Latest version
     */
    public function getLatestVersion()
    {
        $versions = array_keys($this->getMigrationFiles());
        if (empty($versions)) {
            return '1.0.0';
        }
        
        usort($versions, 'version_compare');
        return end($versions);
    }

    /**
     * Check if migration is needed
     * 
     * @return bool True if migration is needed
     */
    public function isMigrationNeeded()
    {
        return version_compare($this->currentVersion, $this->targetVersion, '<');
    }

    /**
     * Run all necessary migrations
     * 
     * @return bool True on success
     * @throws Exception On migration failure
     */
    public function runMigrations()
    {
        if (!$this->isMigrationNeeded()) {
            $this->log('No migration needed. Current version: ' . $this->currentVersion);
            return true;
        }

        $this->log('Starting migration from ' . $this->currentVersion . ' to ' . $this->targetVersion);

        try {
            // Create backup before migration
            $this->createBackup();
            
            // Get migration path
            $migrationPath = $this->getMigrationPath();
            
            if (empty($migrationPath)) {
                throw new Exception('No migration path found from ' . $this->currentVersion . ' to ' . $this->targetVersion);
            }

            // Execute migrations in order
            foreach ($migrationPath as $migration) {
                $this->executeMigration($migration);
            }

            $this->log('Migration completed successfully');
            return true;

        } catch (Exception $e) {
            $this->log('Migration failed: ' . $e->getMessage(), 'error');
            
            // Attempt rollback
            $this->rollback();
            
            throw $e;
        }
    }

    /**
     * Execute single migration
     * 
     * @param array $migration Migration info
     * @return bool True on success
     * @throws Exception On migration failure
     */
    private function executeMigration($migration)
    {
        $this->log('Executing migration: ' . $migration['class']);

        $migrationFile = dirname(__FILE__) . '/' . $migration['file'];
        
        if (!file_exists($migrationFile)) {
            throw new Exception('Migration file not found: ' . $migrationFile);
        }

        require_once $migrationFile;
        
        if (!class_exists($migration['class'])) {
            throw new Exception('Migration class not found: ' . $migration['class']);
        }

        $migrationInstance = new $migration['class']();
        
        if (!method_exists($migrationInstance, 'up')) {
            throw new Exception('Migration class must have "up" method: ' . $migration['class']);
        }

        // Execute migration
        $result = $migrationInstance->up();
        
        if (!$result) {
            throw new Exception('Migration failed: ' . $migration['class']);
        }

        // Update version
        $this->setVersion($migration['to_version']);
        $this->currentVersion = $migration['to_version'];
        
        $this->log('Migration completed: ' . $migration['class']);
        
        return true;
    }

    /**
     * Rollback to previous version
     * 
     * @return bool True on success
     */
    public function rollback()
    {
        $this->log('Starting rollback process');

        try {
            // Restore from backup
            $this->restoreBackup();
            
            $this->log('Rollback completed successfully');
            return true;
            
        } catch (Exception $e) {
            $this->log('Rollback failed: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * Create backup before migration
     * 
     * @return bool True on success
     */
    private function createBackup()
    {
        $this->log('Creating backup before migration');
        
        $timestamp = date('Y_m_d_H_i_s');
        $tables = ['cig_catalog', 'cig_catalog_lang', 'cig_catalog_order', 'cig_catalog_config'];
        
        foreach ($tables as $table) {
            $backupTable = _DB_PREFIX_ . $table . '_backup_' . $timestamp;
            $originalTable = _DB_PREFIX_ . $table;
            
            $sql = 'CREATE TABLE `' . $backupTable . '` LIKE `' . $originalTable . '`';
            if (!Db::getInstance()->execute($sql)) {
                throw new Exception('Failed to create backup table: ' . $backupTable);
            }
            
            $sql = 'INSERT INTO `' . $backupTable . '` SELECT * FROM `' . $originalTable . '`';
            if (!Db::getInstance()->execute($sql)) {
                throw new Exception('Failed to backup data to: ' . $backupTable);
            }
        }
        
        // Store backup info
        $this->setConfig('CIG_CATALOG_LAST_BACKUP', $timestamp);
        
        $this->log('Backup created with timestamp: ' . $timestamp);
        return true;
    }

    /**
     * Restore from backup
     * 
     * @return bool True on success
     */
    private function restoreBackup()
    {
        $timestamp = $this->getConfig('CIG_CATALOG_LAST_BACKUP');
        
        if (!$timestamp) {
            throw new Exception('No backup found to restore from');
        }
        
        $this->log('Restoring from backup: ' . $timestamp);
        
        $tables = ['cig_catalog', 'cig_catalog_lang', 'cig_catalog_order', 'cig_catalog_config'];
        
        foreach ($tables as $table) {
            $backupTable = _DB_PREFIX_ . $table . '_backup_' . $timestamp;
            $originalTable = _DB_PREFIX_ . $table;
            
            // Check if backup table exists
            $sql = 'SHOW TABLES LIKE "' . $backupTable . '"';
            if (!Db::getInstance()->getValue($sql)) {
                throw new Exception('Backup table not found: ' . $backupTable);
            }
            
            // Truncate original table
            $sql = 'TRUNCATE TABLE `' . $originalTable . '`';
            if (!Db::getInstance()->execute($sql)) {
                throw new Exception('Failed to truncate table: ' . $originalTable);
            }
            
            // Restore data
            $sql = 'INSERT INTO `' . $originalTable . '` SELECT * FROM `' . $backupTable . '`';
            if (!Db::getInstance()->execute($sql)) {
                throw new Exception('Failed to restore data from: ' . $backupTable);
            }
        }
        
        $this->log('Backup restored successfully');
        return true;
    }

    /**
     * Get migration path from current to target version
     * 
     * @return array Migration path
     */
    private function getMigrationPath()
    {
        $path = [];
        $currentVersion = $this->currentVersion;
        
        while (version_compare($currentVersion, $this->targetVersion, '<')) {
            $nextMigration = $this->getNextMigration($currentVersion);
            
            if (!$nextMigration) {
                break;
            }
            
            $path[] = $nextMigration;
            $currentVersion = $nextMigration['to_version'];
        }
        
        return $path;
    }

    /**
     * Get next migration from given version
     * 
     * @param string $fromVersion Starting version
     * @return array|null Migration info or null if not found
     */
    private function getNextMigration($fromVersion)
    {
        foreach ($this->migrations as $migration) {
            if ($migration['from_version'] === $fromVersion) {
                return $migration;
            }
        }
        
        return null;
    }

    /**
     * Load available migrations
     */
    private function loadMigrations()
    {
        $this->migrations = $this->getMigrationFiles();
    }

    /**
     * Get migration files from directory
     * 
     * @return array Migration files info
     */
    private function getMigrationFiles()
    {
        $migrations = [];
        $migrationDir = dirname(__FILE__);
        
        $files = glob($migrationDir . '/Migration_*.php');
        
        foreach ($files as $file) {
            $filename = basename($file, '.php');
            
            // Parse migration filename: Migration_1_0_0_to_1_1_0
            if (preg_match('/Migration_(\d+)_(\d+)_(\d+)_to_(\d+)_(\d+)_(\d+)/', $filename, $matches)) {
                $fromVersion = $matches[1] . '.' . $matches[2] . '.' . $matches[3];
                $toVersion = $matches[4] . '.' . $matches[5] . '.' . $matches[6];
                
                $migrations[$toVersion] = [
                    'file' => basename($file),
                    'class' => $filename,
                    'from_version' => $fromVersion,
                    'to_version' => $toVersion
                ];
            }
        }
        
        return $migrations;
    }

    /**
     * Set module version in database
     * 
     * @param string $version Version to set
     * @return bool True on success
     */
    private function setVersion($version)
    {
        $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'cig_catalog_config` 
                (`name`, `value`, `date_add`, `date_upd`) VALUES
                ("CIG_CATALOG_VERSION", "' . pSQL($version) . '", NOW(), NOW())
                ON DUPLICATE KEY UPDATE `value` = "' . pSQL($version) . '", `date_upd` = NOW()';
        
        return Db::getInstance()->execute($sql);
    }

    /**
     * Set configuration value
     * 
     * @param string $name Config name
     * @param string $value Config value
     * @return bool True on success
     */
    private function setConfig($name, $value)
    {
        $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'cig_catalog_config` 
                (`name`, `value`, `date_add`, `date_upd`) VALUES
                ("' . pSQL($name) . '", "' . pSQL($value) . '", NOW(), NOW())
                ON DUPLICATE KEY UPDATE `value` = "' . pSQL($value) . '", `date_upd` = NOW()';
        
        return Db::getInstance()->execute($sql);
    }

    /**
     * Get configuration value
     * 
     * @param string $name Config name
     * @return string|null Config value or null if not found
     */
    private function getConfig($name)
    {
        $sql = 'SELECT `value` FROM `' . _DB_PREFIX_ . 'cig_catalog_config` 
                WHERE `name` = "' . pSQL($name) . '" LIMIT 1';
        
        return Db::getInstance()->getValue($sql);
    }

    /**
     * Log migration message
     * 
     * @param string $message Log message
     * @param string $level Log level (info, error, warning)
     */
    private function log($message, $level = 'info')
    {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = '[' . $timestamp . '] [' . strtoupper($level) . '] ' . $message;
        
        $this->log[] = $logEntry;
        
        if ($this->debug) {
            error_log($logEntry);
        }
        
        // Store in database for persistent logging
        $this->setConfig('CIG_CATALOG_MIGRATION_LOG_' . time(), $logEntry);
    }

    /**
     * Get migration log
     * 
     * @return array Log entries
     */
    public function getLog()
    {
        return $this->log;
    }

    /**
     * Clear old migration logs (keep last 50)
     * 
     * @return bool True on success
     */
    public function clearOldLogs()
    {
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'cig_catalog_config` 
                WHERE `name` LIKE "CIG_CATALOG_MIGRATION_LOG_%" 
                AND `name` NOT IN (
                    SELECT `name` FROM (
                        SELECT `name` FROM `' . _DB_PREFIX_ . 'cig_catalog_config` 
                        WHERE `name` LIKE "CIG_CATALOG_MIGRATION_LOG_%" 
                        ORDER BY `date_add` DESC LIMIT 50
                    ) AS recent_logs
                )';
        
        return Db::getInstance()->execute($sql);
    }
}
