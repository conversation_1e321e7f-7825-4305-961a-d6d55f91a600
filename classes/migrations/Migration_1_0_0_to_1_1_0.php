<?php
/**
 * CIG Katalogy Module - Migration 1.0.0 to 1.1.0
 * 
 * Adds caching support and SEO optimizations
 * 
 * <AUTHOR> Team
 * @copyright 2025 CIG
 * @license   Commercial
 * @version   1.1.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/AbstractMigration.php';

class Migration_1_0_0_to_1_1_0 extends AbstractMigration
{
    /** @var string From version */
    protected $fromVersion = '1.0.0';
    
    /** @var string To version */
    protected $toVersion = '1.1.0';

    /**
     * Execute migration (upgrade)
     * 
     * @return bool True on success
     * @throws Exception On migration failure
     */
    public function up()
    {
        $this->log('Starting migration from ' . $this->fromVersion . ' to ' . $this->toVersion);

        try {
            // Add new configuration options for caching
            $this->addCacheConfigurations();
            
            // Add SEO configuration options
            $this->addSeoConfigurations();
            
            // Add performance indexes
            $this->addPerformanceIndexes();
            
            // Add new columns for SEO
            $this->addSeoColumns();
            
            // Update existing data
            $this->updateExistingData();
            
            // Validate data integrity
            $this->validateDataIntegrity();
            
            $this->log('Migration completed successfully');
            return true;
            
        } catch (Exception $e) {
            $this->log('Migration failed: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Rollback migration (downgrade)
     * 
     * @return bool True on success
     * @throws Exception On rollback failure
     */
    public function down()
    {
        $this->log('Starting rollback from ' . $this->toVersion . ' to ' . $this->fromVersion);

        try {
            // Remove SEO columns
            $this->removeSeoColumns();
            
            // Remove performance indexes
            $this->removePerformanceIndexes();
            
            // Remove new configurations
            $this->removeCacheConfigurations();
            $this->removeSeoConfigurations();
            
            $this->log('Rollback completed successfully');
            return true;
            
        } catch (Exception $e) {
            $this->log('Rollback failed: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Get migration description
     * 
     * @return string Migration description
     */
    public function getDescription()
    {
        return 'Adds caching support, SEO optimizations, and performance improvements';
    }

    /**
     * Add cache configuration options
     * 
     * @return bool True on success
     */
    private function addCacheConfigurations()
    {
        $this->log('Adding cache configuration options');

        $cacheConfigs = [
            'CIG_CATALOG_CACHE_ENABLED' => '1',
            'CIG_CATALOG_CACHE_LIFETIME' => '3600',
            'CIG_CATALOG_CACHE_KEY_PREFIX' => 'cig_catalog_',
            'CIG_CATALOG_CACHE_CLEAR_ON_UPDATE' => '1'
        ];

        foreach ($cacheConfigs as $name => $value) {
            $this->addConfigIfNotExists($name, $value);
        }

        return true;
    }

    /**
     * Add SEO configuration options
     * 
     * @return bool True on success
     */
    private function addSeoConfigurations()
    {
        $this->log('Adding SEO configuration options');

        $seoConfigs = [
            'CIG_CATALOG_SEO_FRIENDLY_URLS' => '1',
            'CIG_CATALOG_META_TITLE_TEMPLATE' => '{catalog_title} - Katalogy',
            'CIG_CATALOG_META_DESCRIPTION_TEMPLATE' => 'Stáhněte si katalog {catalog_title}. {catalog_description}',
            'CIG_CATALOG_CANONICAL_URLS' => '1',
            'CIG_CATALOG_SITEMAP_ENABLED' => '1'
        ];

        foreach ($seoConfigs as $name => $value) {
            $this->addConfigIfNotExists($name, $value);
        }

        return true;
    }

    /**
     * Add performance indexes
     * 
     * @return bool True on success
     */
    private function addPerformanceIndexes()
    {
        $this->log('Adding performance indexes');

        // Add composite index for title and active status
        $this->addIndexIfNotExists(
            'cig_catalog',
            'idx_title_active',
            ['title', 'active']
        );

        // Add index for date_add for sorting
        $this->addIndexIfNotExists(
            'cig_catalog',
            'idx_date_add',
            ['date_add']
        );

        // Add index for position and active for frontend queries
        $this->addIndexIfNotExists(
            'cig_catalog',
            'idx_position_active',
            ['position', 'active']
        );

        // Add index for catalog orders by date
        $this->addIndexIfNotExists(
            'cig_catalog_order',
            'idx_catalog_date',
            ['id_catalog', 'date_add']
        );

        return true;
    }

    /**
     * Add SEO columns
     * 
     * @return bool True on success
     */
    private function addSeoColumns()
    {
        $this->log('Adding SEO columns');

        // Add meta_title column to main table
        $this->addColumnIfNotExists(
            'cig_catalog',
            'meta_title',
            'VARCHAR(255) DEFAULT NULL',
            'description'
        );

        // Add meta_description column to main table
        $this->addColumnIfNotExists(
            'cig_catalog',
            'meta_description',
            'TEXT DEFAULT NULL',
            'meta_title'
        );

        // Add slug column for SEO-friendly URLs
        $this->addColumnIfNotExists(
            'cig_catalog',
            'slug',
            'VARCHAR(255) DEFAULT NULL',
            'meta_description'
        );

        // Add meta_title to language table
        $this->addColumnIfNotExists(
            'cig_catalog_lang',
            'meta_title',
            'VARCHAR(255) DEFAULT NULL',
            'description'
        );

        // Add meta_description to language table
        $this->addColumnIfNotExists(
            'cig_catalog_lang',
            'meta_description',
            'TEXT DEFAULT NULL',
            'meta_title'
        );

        // Add slug to language table
        $this->addColumnIfNotExists(
            'cig_catalog_lang',
            'slug',
            'VARCHAR(255) DEFAULT NULL',
            'meta_description'
        );

        // Add unique index for slug
        $this->addIndexIfNotExists(
            'cig_catalog',
            'idx_slug_unique',
            ['slug'],
            'UNIQUE'
        );

        return true;
    }

    /**
     * Update existing data
     * 
     * @return bool True on success
     */
    private function updateExistingData()
    {
        $this->log('Updating existing data');

        // Generate slugs for existing catalogs
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `slug` = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
                    `title`, " ", "-"), "á", "a"), "č", "c"), "ď", "d"), "é", "e"))
                WHERE `slug` IS NULL OR `slug` = ""';
        
        $this->executeQuery($sql, false);

        // Generate meta titles from titles if not set
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `meta_title` = `title`
                WHERE `meta_title` IS NULL OR `meta_title` = ""';
        
        $this->executeQuery($sql, false);

        // Generate meta descriptions from descriptions if not set
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `meta_description` = LEFT(`description`, 160)
                WHERE `meta_description` IS NULL OR `meta_description` = ""
                AND `description` IS NOT NULL AND `description` != ""';
        
        $this->executeQuery($sql, false);

        // Update language table with same data
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog_lang` cl
                JOIN `' . _DB_PREFIX_ . 'cig_catalog` c ON cl.id_catalog = c.id_catalog
                SET cl.meta_title = cl.title,
                    cl.meta_description = LEFT(cl.description, 160),
                    cl.slug = LOWER(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
                        cl.title, " ", "-"), "á", "a"), "č", "c"), "ď", "d"), "é", "e"))
                WHERE (cl.meta_title IS NULL OR cl.meta_title = "")
                OR (cl.slug IS NULL OR cl.slug = "")';
        
        $this->executeQuery($sql, false);

        return true;
    }

    /**
     * Validate data integrity
     * 
     * @return bool True on success
     * @throws Exception If data integrity issues found
     */
    private function validateDataIntegrity()
    {
        $this->log('Validating data integrity');

        // Check for duplicate slugs
        $sql = 'SELECT COUNT(*) as count, slug 
                FROM `' . _DB_PREFIX_ . 'cig_catalog` 
                WHERE slug IS NOT NULL AND slug != ""
                GROUP BY slug 
                HAVING count > 1';
        
        $duplicates = Db::getInstance()->executeS($sql);
        
        if (!empty($duplicates)) {
            // Fix duplicate slugs by appending ID
            foreach ($duplicates as $duplicate) {
                $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                        SET slug = CONCAT(slug, "-", id_catalog)
                        WHERE slug = "' . pSQL($duplicate['slug']) . '"
                        AND id_catalog != (
                            SELECT min_id FROM (
                                SELECT MIN(id_catalog) as min_id 
                                FROM `' . _DB_PREFIX_ . 'cig_catalog` 
                                WHERE slug = "' . pSQL($duplicate['slug']) . '"
                            ) as subquery
                        )';
                
                $this->executeQuery($sql, false);
            }
        }

        // Ensure all catalogs have slugs
        $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'cig_catalog` 
                WHERE slug IS NULL OR slug = ""';
        
        $missingSlugCount = Db::getInstance()->getValue($sql);
        
        if ($missingSlugCount > 0) {
            throw new Exception('Found ' . $missingSlugCount . ' catalogs without slugs after migration');
        }

        return true;
    }

    /**
     * Remove SEO columns (for rollback)
     * 
     * @return bool True on success
     */
    private function removeSeoColumns()
    {
        $this->log('Removing SEO columns');

        // Remove from main table
        $this->dropColumnIfExists('cig_catalog', 'meta_title');
        $this->dropColumnIfExists('cig_catalog', 'meta_description');
        $this->dropColumnIfExists('cig_catalog', 'slug');

        // Remove from language table
        $this->dropColumnIfExists('cig_catalog_lang', 'meta_title');
        $this->dropColumnIfExists('cig_catalog_lang', 'meta_description');
        $this->dropColumnIfExists('cig_catalog_lang', 'slug');

        return true;
    }

    /**
     * Remove performance indexes (for rollback)
     * 
     * @return bool True on success
     */
    private function removePerformanceIndexes()
    {
        $this->log('Removing performance indexes');

        $this->dropIndexIfExists('cig_catalog', 'idx_title_active');
        $this->dropIndexIfExists('cig_catalog', 'idx_date_add');
        $this->dropIndexIfExists('cig_catalog', 'idx_position_active');
        $this->dropIndexIfExists('cig_catalog', 'idx_slug_unique');
        $this->dropIndexIfExists('cig_catalog_order', 'idx_catalog_date');

        return true;
    }

    /**
     * Remove cache configurations (for rollback)
     * 
     * @return bool True on success
     */
    private function removeCacheConfigurations()
    {
        $this->log('Removing cache configurations');

        $cacheConfigs = [
            'CIG_CATALOG_CACHE_ENABLED',
            'CIG_CATALOG_CACHE_LIFETIME',
            'CIG_CATALOG_CACHE_KEY_PREFIX',
            'CIG_CATALOG_CACHE_CLEAR_ON_UPDATE'
        ];

        foreach ($cacheConfigs as $name) {
            $this->removeConfig($name);
        }

        return true;
    }

    /**
     * Remove SEO configurations (for rollback)
     * 
     * @return bool True on success
     */
    private function removeSeoConfigurations()
    {
        $this->log('Removing SEO configurations');

        $seoConfigs = [
            'CIG_CATALOG_SEO_FRIENDLY_URLS',
            'CIG_CATALOG_META_TITLE_TEMPLATE',
            'CIG_CATALOG_META_DESCRIPTION_TEMPLATE',
            'CIG_CATALOG_CANONICAL_URLS',
            'CIG_CATALOG_SITEMAP_ENABLED'
        ];

        foreach ($seoConfigs as $name) {
            $this->removeConfig($name);
        }

        return true;
    }
}
