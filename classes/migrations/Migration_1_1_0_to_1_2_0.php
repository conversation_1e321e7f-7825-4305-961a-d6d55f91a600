<?php
/**
 * CIG Katalogy Module - Migration 1.1.0 to 1.2.0
 * 
 * Adds category support for better catalog organization
 * 
 * <AUTHOR> Team
 * @copyright 2025 CIG
 * @license   Commercial
 * @version   1.2.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/AbstractMigration.php';

class Migration_1_1_0_to_1_2_0 extends AbstractMigration
{
    /** @var string From version */
    protected $fromVersion = '1.1.0';
    
    /** @var string To version */
    protected $toVersion = '1.2.0';

    /**
     * Execute migration (upgrade)
     * 
     * @return bool True on success
     * @throws Exception On migration failure
     */
    public function up()
    {
        $this->log('Starting migration from ' . $this->fromVersion . ' to ' . $this->toVersion);

        try {
            // Create category table
            $this->createCategoryTable();
            
            // Create category language table
            $this->createCategoryLangTable();
            
            // Add category column to catalog table
            $this->addCategoryColumnToCatalog();
            
            // Add category configurations
            $this->addCategoryConfigurations();
            
            // Create default categories
            $this->createDefaultCategories();
            
            // Add category indexes
            $this->addCategoryIndexes();
            
            // Validate data integrity
            $this->validateDataIntegrity();
            
            $this->log('Migration completed successfully');
            return true;
            
        } catch (Exception $e) {
            $this->log('Migration failed: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Rollback migration (downgrade)
     * 
     * @return bool True on success
     * @throws Exception On rollback failure
     */
    public function down()
    {
        $this->log('Starting rollback from ' . $this->toVersion . ' to ' . $this->fromVersion);

        try {
            // Remove category column from catalog table
            $this->removeCategoryColumnFromCatalog();
            
            // Drop category tables
            $this->dropCategoryTables();
            
            // Remove category configurations
            $this->removeCategoryConfigurations();
            
            $this->log('Rollback completed successfully');
            return true;
            
        } catch (Exception $e) {
            $this->log('Rollback failed: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Get migration description
     * 
     * @return string Migration description
     */
    public function getDescription()
    {
        return 'Adds category support for better catalog organization and filtering';
    }

    /**
     * Create category table
     * 
     * @return bool True on success
     */
    private function createCategoryTable()
    {
        $this->log('Creating category table');

        if ($this->tableExists('cig_catalog_category')) {
            $this->log('Category table already exists');
            return true;
        }

        $sql = 'CREATE TABLE `' . _DB_PREFIX_ . 'cig_catalog_category` (
            `id_category` int(11) NOT NULL AUTO_INCREMENT,
            `id_parent` int(11) DEFAULT 0,
            `name` varchar(255) NOT NULL,
            `description` text,
            `image_path` varchar(500),
            `color` varchar(7) DEFAULT "#007cba",
            `icon` varchar(100),
            `position` int(11) DEFAULT 0,
            `active` tinyint(1) DEFAULT 1,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_category`),
            KEY `id_parent` (`id_parent`),
            KEY `position` (`position`),
            KEY `active` (`active`),
            KEY `parent_position` (`id_parent`, `position`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4';

        return $this->executeQuery($sql, false);
    }

    /**
     * Create category language table
     * 
     * @return bool True on success
     */
    private function createCategoryLangTable()
    {
        $this->log('Creating category language table');

        if ($this->tableExists('cig_catalog_category_lang')) {
            $this->log('Category language table already exists');
            return true;
        }

        $sql = 'CREATE TABLE `' . _DB_PREFIX_ . 'cig_catalog_category_lang` (
            `id_category` int(11) NOT NULL,
            `id_lang` int(11) NOT NULL,
            `name` varchar(255) NOT NULL,
            `description` text,
            `meta_title` varchar(255),
            `meta_description` text,
            `slug` varchar(255),
            PRIMARY KEY (`id_category`, `id_lang`),
            KEY `id_lang` (`id_lang`),
            KEY `slug` (`slug`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4';

        return $this->executeQuery($sql, false);
    }

    /**
     * Add category column to catalog table
     * 
     * @return bool True on success
     */
    private function addCategoryColumnToCatalog()
    {
        $this->log('Adding category column to catalog table');

        // Add id_category column
        $this->addColumnIfNotExists(
            'cig_catalog',
            'id_category',
            'int(11) DEFAULT NULL',
            'id_catalog'
        );

        // Add foreign key constraint
        $sql = 'ALTER TABLE `' . _DB_PREFIX_ . 'cig_catalog` 
                ADD CONSTRAINT `fk_catalog_category` 
                FOREIGN KEY (`id_category`) 
                REFERENCES `' . _DB_PREFIX_ . 'cig_catalog_category` (`id_category`) 
                ON DELETE SET NULL ON UPDATE CASCADE';

        try {
            $this->executeQuery($sql, false);
        } catch (Exception $e) {
            // Foreign key might already exist, check if it's the constraint error
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                $this->log('Foreign key constraint already exists');
            } else {
                throw $e;
            }
        }

        return true;
    }

    /**
     * Add category configurations
     * 
     * @return bool True on success
     */
    private function addCategoryConfigurations()
    {
        $this->log('Adding category configurations');

        $categoryConfigs = [
            'CIG_CATALOG_CATEGORIES_ENABLED' => '1',
            'CIG_CATALOG_CATEGORY_TREE_DEPTH' => '3',
            'CIG_CATALOG_SHOW_CATEGORY_COUNT' => '1',
            'CIG_CATALOG_CATEGORY_DEFAULT_COLOR' => '#007cba',
            'CIG_CATALOG_CATEGORY_FILTER_ENABLED' => '1'
        ];

        foreach ($categoryConfigs as $name => $value) {
            $this->addConfigIfNotExists($name, $value);
        }

        return true;
    }

    /**
     * Create default categories
     * 
     * @return bool True on success
     */
    private function createDefaultCategories()
    {
        $this->log('Creating default categories');

        // Check if categories already exist
        $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'cig_catalog_category`';
        $categoryCount = Db::getInstance()->getValue($sql);

        if ($categoryCount > 0) {
            $this->log('Categories already exist, skipping default creation');
            return true;
        }

        // Get available languages
        $sql = 'SELECT id_lang FROM `' . _DB_PREFIX_ . 'lang` WHERE active = 1';
        $languages = Db::getInstance()->executeS($sql);

        if (empty($languages)) {
            throw new Exception('No active languages found');
        }

        // Default categories
        $defaultCategories = [
            [
                'name' => 'Elektronika',
                'description' => 'Katalogy elektronických produktů',
                'color' => '#007cba',
                'icon' => 'fa-microchip'
            ],
            [
                'name' => 'Nábytek',
                'description' => 'Katalogy nábytku a bytových doplňků',
                'color' => '#8B4513',
                'icon' => 'fa-couch'
            ],
            [
                'name' => 'Móda',
                'description' => 'Katalogy oblečení a módních doplňků',
                'color' => '#FF69B4',
                'icon' => 'fa-tshirt'
            ],
            [
                'name' => 'Automotive',
                'description' => 'Katalogy automobilových produktů',
                'color' => '#FF4500',
                'icon' => 'fa-car'
            ],
            [
                'name' => 'Ostatní',
                'description' => 'Ostatní katalogy',
                'color' => '#808080',
                'icon' => 'fa-folder'
            ]
        ];

        foreach ($defaultCategories as $position => $category) {
            // Insert category
            $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'cig_catalog_category` 
                    (`id_parent`, `name`, `description`, `color`, `icon`, `position`, `active`, `date_add`, `date_upd`) 
                    VALUES (0, "' . pSQL($category['name']) . '", "' . pSQL($category['description']) . '", 
                           "' . pSQL($category['color']) . '", "' . pSQL($category['icon']) . '", 
                           ' . (int)($position + 1) . ', 1, NOW(), NOW())';

            $this->executeQuery($sql, false);
            
            $categoryId = Db::getInstance()->Insert_ID();

            // Insert language entries
            foreach ($languages as $language) {
                $slug = strtolower(str_replace(' ', '-', 
                    str_replace(['á', 'č', 'ď', 'é', 'ě', 'í', 'ň', 'ó', 'ř', 'š', 'ť', 'ú', 'ů', 'ý', 'ž'], 
                               ['a', 'c', 'd', 'e', 'e', 'i', 'n', 'o', 'r', 's', 't', 'u', 'u', 'y', 'z'], 
                               $category['name'])));

                $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'cig_catalog_category_lang` 
                        (`id_category`, `id_lang`, `name`, `description`, `meta_title`, `meta_description`, `slug`) 
                        VALUES (' . (int)$categoryId . ', ' . (int)$language['id_lang'] . ', 
                               "' . pSQL($category['name']) . '", "' . pSQL($category['description']) . '",
                               "' . pSQL($category['name']) . ' - Katalogy", 
                               "' . pSQL($category['description']) . '", "' . pSQL($slug) . '")';

                $this->executeQuery($sql, false);
            }
        }

        return true;
    }

    /**
     * Add category indexes
     * 
     * @return bool True on success
     */
    private function addCategoryIndexes()
    {
        $this->log('Adding category indexes');

        // Add index for catalog category filtering
        $this->addIndexIfNotExists(
            'cig_catalog',
            'idx_category_active',
            ['id_category', 'active']
        );

        // Add index for category position sorting
        $this->addIndexIfNotExists(
            'cig_catalog',
            'idx_category_position',
            ['id_category', 'position']
        );

        return true;
    }

    /**
     * Validate data integrity
     * 
     * @return bool True on success
     * @throws Exception If data integrity issues found
     */
    private function validateDataIntegrity()
    {
        $this->log('Validating data integrity');

        // Check if all categories have language entries
        $sql = 'SELECT c.id_category, c.name 
                FROM `' . _DB_PREFIX_ . 'cig_catalog_category` c
                WHERE NOT EXISTS (
                    SELECT 1 FROM `' . _DB_PREFIX_ . 'cig_catalog_category_lang` cl 
                    WHERE cl.id_category = c.id_category
                )';

        $categoriesWithoutLang = Db::getInstance()->executeS($sql);

        if (!empty($categoriesWithoutLang)) {
            throw new Exception('Found categories without language entries: ' . 
                               implode(', ', array_column($categoriesWithoutLang, 'name')));
        }

        // Check for duplicate category slugs
        $sql = 'SELECT COUNT(*) as count, slug, id_lang
                FROM `' . _DB_PREFIX_ . 'cig_catalog_category_lang` 
                WHERE slug IS NOT NULL AND slug != ""
                GROUP BY slug, id_lang 
                HAVING count > 1';

        $duplicateSlugs = Db::getInstance()->executeS($sql);

        if (!empty($duplicateSlugs)) {
            // Fix duplicate slugs
            foreach ($duplicateSlugs as $duplicate) {
                $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog_category_lang` 
                        SET slug = CONCAT(slug, "-", id_category)
                        WHERE slug = "' . pSQL($duplicate['slug']) . '"
                        AND id_lang = ' . (int)$duplicate['id_lang'] . '
                        AND id_category != (
                            SELECT min_id FROM (
                                SELECT MIN(id_category) as min_id 
                                FROM `' . _DB_PREFIX_ . 'cig_catalog_category_lang` 
                                WHERE slug = "' . pSQL($duplicate['slug']) . '"
                                AND id_lang = ' . (int)$duplicate['id_lang'] . '
                            ) as subquery
                        )';

                $this->executeQuery($sql, false);
            }
        }

        return true;
    }

    /**
     * Remove category column from catalog table (for rollback)
     * 
     * @return bool True on success
     */
    private function removeCategoryColumnFromCatalog()
    {
        $this->log('Removing category column from catalog table');

        // Remove foreign key constraint first
        try {
            $sql = 'ALTER TABLE `' . _DB_PREFIX_ . 'cig_catalog` 
                    DROP FOREIGN KEY `fk_catalog_category`';
            $this->executeQuery($sql, false);
        } catch (Exception $e) {
            // Constraint might not exist
            $this->log('Foreign key constraint might not exist: ' . $e->getMessage());
        }

        // Remove indexes
        $this->dropIndexIfExists('cig_catalog', 'idx_category_active');
        $this->dropIndexIfExists('cig_catalog', 'idx_category_position');

        // Remove column
        $this->dropColumnIfExists('cig_catalog', 'id_category');

        return true;
    }

    /**
     * Drop category tables (for rollback)
     * 
     * @return bool True on success
     */
    private function dropCategoryTables()
    {
        $this->log('Dropping category tables');

        // Drop language table first (foreign key dependency)
        $sql = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'cig_catalog_category_lang`';
        $this->executeQuery($sql, false);

        // Drop main category table
        $sql = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'cig_catalog_category`';
        $this->executeQuery($sql, false);

        return true;
    }

    /**
     * Remove category configurations (for rollback)
     * 
     * @return bool True on success
     */
    private function removeCategoryConfigurations()
    {
        $this->log('Removing category configurations');

        $categoryConfigs = [
            'CIG_CATALOG_CATEGORIES_ENABLED',
            'CIG_CATALOG_CATEGORY_TREE_DEPTH',
            'CIG_CATALOG_SHOW_CATEGORY_COUNT',
            'CIG_CATALOG_CATEGORY_DEFAULT_COLOR',
            'CIG_CATALOG_CATEGORY_FILTER_ENABLED'
        ];

        foreach ($categoryConfigs as $name) {
            $this->removeConfig($name);
        }

        return true;
    }
}
