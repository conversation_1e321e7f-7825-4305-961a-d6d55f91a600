<?php
/**
 * CIG Katalogy Module - Migration 1.2.0 to 1.3.0
 * 
 * Adds advanced SEO features and analytics support
 * 
 * <AUTHOR> Team
 * @copyright 2025 CIG
 * @license   Commercial
 * @version   1.3.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/AbstractMigration.php';

class Migration_1_2_0_to_1_3_0 extends AbstractMigration
{
    /** @var string From version */
    protected $fromVersion = '1.2.0';
    
    /** @var string To version */
    protected $toVersion = '1.3.0';

    /**
     * Execute migration (upgrade)
     * 
     * @return bool True on success
     * @throws Exception On migration failure
     */
    public function up()
    {
        $this->log('Starting migration from ' . $this->fromVersion . ' to ' . $this->toVersion);

        try {
            // Create analytics table
            $this->createAnalyticsTable();
            
            // Add SEO columns to existing tables
            $this->addAdvancedSeoColumns();
            
            // Add analytics configurations
            $this->addAnalyticsConfigurations();
            
            // Add advanced SEO configurations
            $this->addAdvancedSeoConfigurations();
            
            // Add analytics indexes
            $this->addAnalyticsIndexes();
            
            // Update existing data with new SEO features
            $this->updateExistingDataForSeo();
            
            // Validate data integrity
            $this->validateDataIntegrity();
            
            $this->log('Migration completed successfully');
            return true;
            
        } catch (Exception $e) {
            $this->log('Migration failed: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Rollback migration (downgrade)
     * 
     * @return bool True on success
     * @throws Exception On rollback failure
     */
    public function down()
    {
        $this->log('Starting rollback from ' . $this->toVersion . ' to ' . $this->fromVersion);

        try {
            // Remove advanced SEO columns
            $this->removeAdvancedSeoColumns();
            
            // Drop analytics table
            $this->dropAnalyticsTable();
            
            // Remove configurations
            $this->removeAnalyticsConfigurations();
            $this->removeAdvancedSeoConfigurations();
            
            $this->log('Rollback completed successfully');
            return true;
            
        } catch (Exception $e) {
            $this->log('Rollback failed: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Get migration description
     * 
     * @return string Migration description
     */
    public function getDescription()
    {
        return 'Adds advanced SEO features, analytics tracking, and performance monitoring';
    }

    /**
     * Create analytics table
     * 
     * @return bool True on success
     */
    private function createAnalyticsTable()
    {
        $this->log('Creating analytics table');

        if ($this->tableExists('cig_catalog_analytics')) {
            $this->log('Analytics table already exists');
            return true;
        }

        $sql = 'CREATE TABLE `' . _DB_PREFIX_ . 'cig_catalog_analytics` (
            `id_analytics` int(11) NOT NULL AUTO_INCREMENT,
            `id_catalog` int(11) NOT NULL,
            `event_type` varchar(50) NOT NULL,
            `user_ip` varchar(45),
            `user_agent` text,
            `referer` varchar(500),
            `page_url` varchar(500),
            `session_id` varchar(100),
            `customer_id` int(11) DEFAULT NULL,
            `country_code` varchar(2),
            `device_type` varchar(20),
            `browser` varchar(50),
            `os` varchar(50),
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_analytics`),
            KEY `id_catalog` (`id_catalog`),
            KEY `event_type` (`event_type`),
            KEY `date_add` (`date_add`),
            KEY `session_id` (`session_id`),
            KEY `customer_id` (`customer_id`),
            KEY `catalog_event_date` (`id_catalog`, `event_type`, `date_add`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4';

        return $this->executeQuery($sql, false);
    }

    /**
     * Add advanced SEO columns
     * 
     * @return bool True on success
     */
    private function addAdvancedSeoColumns()
    {
        $this->log('Adding advanced SEO columns');

        // Add to catalog table
        $this->addColumnIfNotExists(
            'cig_catalog',
            'og_title',
            'VARCHAR(255) DEFAULT NULL',
            'meta_description'
        );

        $this->addColumnIfNotExists(
            'cig_catalog',
            'og_description',
            'TEXT DEFAULT NULL',
            'og_title'
        );

        $this->addColumnIfNotExists(
            'cig_catalog',
            'og_image',
            'VARCHAR(500) DEFAULT NULL',
            'og_description'
        );

        $this->addColumnIfNotExists(
            'cig_catalog',
            'twitter_title',
            'VARCHAR(255) DEFAULT NULL',
            'og_image'
        );

        $this->addColumnIfNotExists(
            'cig_catalog',
            'twitter_description',
            'TEXT DEFAULT NULL',
            'twitter_title'
        );

        $this->addColumnIfNotExists(
            'cig_catalog',
            'twitter_image',
            'VARCHAR(500) DEFAULT NULL',
            'twitter_description'
        );

        $this->addColumnIfNotExists(
            'cig_catalog',
            'canonical_url',
            'VARCHAR(500) DEFAULT NULL',
            'twitter_image'
        );

        $this->addColumnIfNotExists(
            'cig_catalog',
            'robots_meta',
            'VARCHAR(100) DEFAULT "index,follow"',
            'canonical_url'
        );

        $this->addColumnIfNotExists(
            'cig_catalog',
            'schema_markup',
            'TEXT DEFAULT NULL',
            'robots_meta'
        );

        // Add to language table
        $this->addColumnIfNotExists(
            'cig_catalog_lang',
            'og_title',
            'VARCHAR(255) DEFAULT NULL',
            'meta_description'
        );

        $this->addColumnIfNotExists(
            'cig_catalog_lang',
            'og_description',
            'TEXT DEFAULT NULL',
            'og_title'
        );

        $this->addColumnIfNotExists(
            'cig_catalog_lang',
            'twitter_title',
            'VARCHAR(255) DEFAULT NULL',
            'og_description'
        );

        $this->addColumnIfNotExists(
            'cig_catalog_lang',
            'twitter_description',
            'TEXT DEFAULT NULL',
            'twitter_title'
        );

        return true;
    }

    /**
     * Add analytics configurations
     * 
     * @return bool True on success
     */
    private function addAnalyticsConfigurations()
    {
        $this->log('Adding analytics configurations');

        $analyticsConfigs = [
            'CIG_CATALOG_ANALYTICS_ENABLED' => '1',
            'CIG_CATALOG_TRACK_VIEWS' => '1',
            'CIG_CATALOG_TRACK_DOWNLOADS' => '1',
            'CIG_CATALOG_TRACK_ORDERS' => '1',
            'CIG_CATALOG_ANALYTICS_RETENTION_DAYS' => '365',
            'CIG_CATALOG_GOOGLE_ANALYTICS_ID' => '',
            'CIG_CATALOG_GOOGLE_TAG_MANAGER_ID' => '',
            'CIG_CATALOG_FACEBOOK_PIXEL_ID' => ''
        ];

        foreach ($analyticsConfigs as $name => $value) {
            $this->addConfigIfNotExists($name, $value);
        }

        return true;
    }

    /**
     * Add advanced SEO configurations
     * 
     * @return bool True on success
     */
    private function addAdvancedSeoConfigurations()
    {
        $this->log('Adding advanced SEO configurations');

        $seoConfigs = [
            'CIG_CATALOG_STRUCTURED_DATA_ENABLED' => '1',
            'CIG_CATALOG_OPEN_GRAPH_ENABLED' => '1',
            'CIG_CATALOG_TWITTER_CARDS_ENABLED' => '1',
            'CIG_CATALOG_BREADCRUMBS_ENABLED' => '1',
            'CIG_CATALOG_HREFLANG_ENABLED' => '1',
            'CIG_CATALOG_ROBOTS_TXT_ENABLED' => '1',
            'CIG_CATALOG_XML_SITEMAP_ENABLED' => '1',
            'CIG_CATALOG_SITEMAP_PRIORITY' => '0.8',
            'CIG_CATALOG_SITEMAP_CHANGEFREQ' => 'weekly'
        ];

        foreach ($seoConfigs as $name => $value) {
            $this->addConfigIfNotExists($name, $value);
        }

        return true;
    }

    /**
     * Add analytics indexes
     * 
     * @return bool True on success
     */
    private function addAnalyticsIndexes()
    {
        $this->log('Adding analytics indexes');

        // Add foreign key constraint for analytics
        $sql = 'ALTER TABLE `' . _DB_PREFIX_ . 'cig_catalog_analytics` 
                ADD CONSTRAINT `fk_analytics_catalog` 
                FOREIGN KEY (`id_catalog`) 
                REFERENCES `' . _DB_PREFIX_ . 'cig_catalog` (`id_catalog`) 
                ON DELETE CASCADE ON UPDATE CASCADE';

        try {
            $this->executeQuery($sql, false);
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                $this->log('Foreign key constraint already exists');
            } else {
                throw $e;
            }
        }

        return true;
    }

    /**
     * Update existing data with new SEO features
     * 
     * @return bool True on success
     */
    private function updateExistingDataForSeo()
    {
        $this->log('Updating existing data with new SEO features');

        // Generate Open Graph titles from meta titles
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `og_title` = `meta_title`
                WHERE `og_title` IS NULL AND `meta_title` IS NOT NULL';
        
        $this->executeQuery($sql, false);

        // Generate Open Graph descriptions from meta descriptions
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `og_description` = `meta_description`
                WHERE `og_description` IS NULL AND `meta_description` IS NOT NULL';
        
        $this->executeQuery($sql, false);

        // Generate Twitter titles from Open Graph titles
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `twitter_title` = `og_title`
                WHERE `twitter_title` IS NULL AND `og_title` IS NOT NULL';
        
        $this->executeQuery($sql, false);

        // Generate Twitter descriptions from Open Graph descriptions
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `twitter_description` = `og_description`
                WHERE `twitter_description` IS NULL AND `og_description` IS NOT NULL';
        
        $this->executeQuery($sql, false);

        // Set Open Graph images from existing images
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `og_image` = `image_path`, `twitter_image` = `image_path`
                WHERE `og_image` IS NULL AND `image_path` IS NOT NULL AND `image_path` != ""';
        
        $this->executeQuery($sql, false);

        // Update language table
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog_lang` 
                SET `og_title` = `meta_title`,
                    `og_description` = `meta_description`,
                    `twitter_title` = `meta_title`,
                    `twitter_description` = `meta_description`
                WHERE (`og_title` IS NULL OR `og_title` = "")
                AND `meta_title` IS NOT NULL AND `meta_title` != ""';
        
        $this->executeQuery($sql, false);

        // Generate basic schema markup for catalogs
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                SET `schema_markup` = CONCAT(\'{"@context":"https://schema.org","@type":"Product","name":"\', 
                    REPLACE(`title`, \'"\', \'\\"\'), \'","description":"\', 
                    REPLACE(COALESCE(`description`, ""), \'"\', \'\\"\'), \'"}\')
                WHERE `schema_markup` IS NULL AND `title` IS NOT NULL';
        
        $this->executeQuery($sql, false);

        return true;
    }

    /**
     * Validate data integrity
     * 
     * @return bool True on success
     * @throws Exception If data integrity issues found
     */
    private function validateDataIntegrity()
    {
        $this->log('Validating data integrity');

        // Check if analytics table was created properly
        if (!$this->tableExists('cig_catalog_analytics')) {
            throw new Exception('Analytics table was not created properly');
        }

        // Check if all catalogs have basic SEO data
        $sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'cig_catalog` 
                WHERE `active` = 1 
                AND (`og_title` IS NULL OR `og_title` = "" 
                     OR `og_description` IS NULL OR `og_description` = "")';
        
        $missingOgCount = Db::getInstance()->getValue($sql);
        
        if ($missingOgCount > 0) {
            $this->log('Warning: ' . $missingOgCount . ' active catalogs missing Open Graph data');
        }

        // Validate schema markup JSON
        $sql = 'SELECT id_catalog, schema_markup FROM `' . _DB_PREFIX_ . 'cig_catalog` 
                WHERE `schema_markup` IS NOT NULL AND `schema_markup` != ""';
        
        $schemas = Db::getInstance()->executeS($sql);
        
        foreach ($schemas as $schema) {
            $decoded = json_decode($schema['schema_markup'], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->log('Warning: Invalid JSON schema markup for catalog ID ' . $schema['id_catalog']);
                
                // Fix invalid schema markup
                $sql = 'UPDATE `' . _DB_PREFIX_ . 'cig_catalog` 
                        SET `schema_markup` = NULL 
                        WHERE `id_catalog` = ' . (int)$schema['id_catalog'];
                
                $this->executeQuery($sql, false);
            }
        }

        return true;
    }

    /**
     * Remove advanced SEO columns (for rollback)
     * 
     * @return bool True on success
     */
    private function removeAdvancedSeoColumns()
    {
        $this->log('Removing advanced SEO columns');

        // Remove from main table
        $seoColumns = [
            'og_title', 'og_description', 'og_image',
            'twitter_title', 'twitter_description', 'twitter_image',
            'canonical_url', 'robots_meta', 'schema_markup'
        ];

        foreach ($seoColumns as $column) {
            $this->dropColumnIfExists('cig_catalog', $column);
        }

        // Remove from language table
        $langSeoColumns = ['og_title', 'og_description', 'twitter_title', 'twitter_description'];

        foreach ($langSeoColumns as $column) {
            $this->dropColumnIfExists('cig_catalog_lang', $column);
        }

        return true;
    }

    /**
     * Drop analytics table (for rollback)
     * 
     * @return bool True on success
     */
    private function dropAnalyticsTable()
    {
        $this->log('Dropping analytics table');

        // Remove foreign key constraint first
        try {
            $sql = 'ALTER TABLE `' . _DB_PREFIX_ . 'cig_catalog_analytics` 
                    DROP FOREIGN KEY `fk_analytics_catalog`';
            $this->executeQuery($sql, false);
        } catch (Exception $e) {
            $this->log('Foreign key constraint might not exist: ' . $e->getMessage());
        }

        // Drop table
        $sql = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'cig_catalog_analytics`';
        $this->executeQuery($sql, false);

        return true;
    }

    /**
     * Remove analytics configurations (for rollback)
     * 
     * @return bool True on success
     */
    private function removeAnalyticsConfigurations()
    {
        $this->log('Removing analytics configurations');

        $analyticsConfigs = [
            'CIG_CATALOG_ANALYTICS_ENABLED',
            'CIG_CATALOG_TRACK_VIEWS',
            'CIG_CATALOG_TRACK_DOWNLOADS',
            'CIG_CATALOG_TRACK_ORDERS',
            'CIG_CATALOG_ANALYTICS_RETENTION_DAYS',
            'CIG_CATALOG_GOOGLE_ANALYTICS_ID',
            'CIG_CATALOG_GOOGLE_TAG_MANAGER_ID',
            'CIG_CATALOG_FACEBOOK_PIXEL_ID'
        ];

        foreach ($analyticsConfigs as $name) {
            $this->removeConfig($name);
        }

        return true;
    }

    /**
     * Remove advanced SEO configurations (for rollback)
     * 
     * @return bool True on success
     */
    private function removeAdvancedSeoConfigurations()
    {
        $this->log('Removing advanced SEO configurations');

        $seoConfigs = [
            'CIG_CATALOG_STRUCTURED_DATA_ENABLED',
            'CIG_CATALOG_OPEN_GRAPH_ENABLED',
            'CIG_CATALOG_TWITTER_CARDS_ENABLED',
            'CIG_CATALOG_BREADCRUMBS_ENABLED',
            'CIG_CATALOG_HREFLANG_ENABLED',
            'CIG_CATALOG_ROBOTS_TXT_ENABLED',
            'CIG_CATALOG_XML_SITEMAP_ENABLED',
            'CIG_CATALOG_SITEMAP_PRIORITY',
            'CIG_CATALOG_SITEMAP_CHANGEFREQ'
        ];

        foreach ($seoConfigs as $name) {
            $this->removeConfig($name);
        }

        return true;
    }
}
